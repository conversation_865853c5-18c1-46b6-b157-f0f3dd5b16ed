/* eslint-disable eslint-comments/disable-enable-pair */
/* eslint-disable max-statements */
/* eslint-disable max-lines */

import type { Ctx } from '@milkdown/ctx';
import { Node, Slice } from '@milkdown/prose/model';

import {
  editorStateOptionsCtx,
  prosePluginsCtx,
  schemaCtx,
  editorViewCtx,
} from '@milkdown/core';

import { EditorState, Plugin } from '@milkdown/prose/state';
import React, {
  useEffect,
  useRef,
  forwardRef,
  useImperativeHandle,
  useContext,
  useState,
} from 'react';
import { Crepe, CrepeFeature } from '@/components/Crepe';
import { EditorView } from '@milkdown/prose/view';
import { replaceAll } from '@milkdown/utils';
import {
  toggleStrongCommand,
  toggleEmphasisCommand,
  // linkSchema,
  blockquoteSchema,
  bulletListSchema,
  codeBlockSchema,
  orderedListSchema,
  toggleInlineCodeCommand,
  textSchema,
  htmlSchema,
} from '@milkdown/preset-commonmark';
import { linkTooltipAPI } from '@/components/Crepe/components/link-tooltip';

import { useWidgetViewFactory } from '@prosemirror-adapter/react';

import { GroupMemberItem } from '@ht/openim-wasm-client-sdk';
import { useDeepCompareEffect } from 'ahooks';
import { getMarkdownFromJson } from '@/utils/parserMdToHtml';
import InputFooterRender from '@/components/Channel/components/MessageInput/InputFooterRender';
import { EditorContext } from './editorContent';
import {
  pastePlugin,
  customKeymap,
  groupAnnouncementKeymap,
  insertTextAtCursor,
} from './util';
import styles from './index.less';
import { MentionsPluginDropdownView } from './plugin-mention/MentionDrop';
import { MentionsPlugin, MentionsPluginOptions } from './plugin-mention';
import { MentionsOptions } from './plugin-mention/plugin';

interface EditMdProps {
  groupMemberList?: GroupMemberItem[];
  showFormater?: boolean;
  defaultValue?: string;
  defaultValueType?: 'nodeType' | 'markdownType';
  isForGroupAnnouncement?: boolean;
  flush?: boolean; // flush is true, the editor state will be re-created.
  inputStyle?: any;
  onChange?: (value: string) => void;
  onFocus?: (ctx: Ctx) => void;
  onBlur?: (ctx: Ctx) => void;
  onError?: (error: Error) => void;
  onPaste?: (view: EditorView, event: ClipboardEvent) => boolean;
  onCompositionStart?: () => void;
  onCompositionEnd?: () => void;
  onKeyDown?: (e: any) => void;
  onMention?: (value: GroupMemberItem) => void;
  onMentionRemove?: (item: { id: string; name: string }) => void;
  hideBtn?: boolean;
  disabledBtn?: boolean;
  uploadMsg?: (data: any, type: string, isCapture?: boolean) => void;
  setShowFormater: (val: boolean) => void;
  conversationID?: string;
  cloudUpload: (docInfo: any) => void;
  isGroup: boolean;
  isMultiSession: boolean;
}
export const FEATURES = {
  [CrepeFeature.Cursor]: false,
  [CrepeFeature.ListItem]: true,
  [CrepeFeature.LinkTooltip]: true,
  [CrepeFeature.ImageBlock]: true,
  [CrepeFeature.BlockEdit]: false,
  [CrepeFeature.Placeholder]: false,
  [CrepeFeature.Toolbar]: false,
  [CrepeFeature.CodeMirror]: true,
  [CrepeFeature.Table]: false,
  [CrepeFeature.Latex]: false,
};
export const featureConfigs = {
  // [CrepeFeature.CodeMirror]: {
  //   // 配置CodeMirror主题
  //   theme: [materialDark],
  //   extensions: [material, javascript({ jsx: true })],
  // },
  [CrepeFeature.LinkTooltip]: {
    inputPlaceholder: '输入链接',
  },
};
/**
 * Markdown Editor Component
 */
const EditMdComponent = (
  {
    groupMemberList,
    showFormater = true,
    defaultValue = '',
    defaultValueType = 'nodeType',
    isForGroupAnnouncement = false,
    flush = false,
    inputStyle = {},
    onChange,
    onError,
    onFocus,
    onBlur,
    onPaste,
    onCompositionStart,
    onCompositionEnd,
    onKeyDown,
    onMention,
    onMentionRemove = (node) => {},
    hideBtn = false,
    disabledBtn = false,
    uploadMsg,
    setShowFormater,
    conversationID,
    cloudUpload,
    isGroup,
    isMultiSession,
  }: EditMdProps,
  ref: any
) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { editorInstance, setEditorInstance } = useContext(EditorContext);
  const widgetViewFactory = useWidgetViewFactory();
  const mentions = MentionsPlugin(widgetViewFactory);
  const [activeMarks, setActiveMarks] = useState<Set<string>>(new Set());
  // const { editor, setEditor } = useContext();

  useDeepCompareEffect(() => {
    if (!containerRef.current) {
      return;
    } else if (containerRef.current == null && defaultValue == null) {
      return;
    }

    let defaultValueTemp = defaultValue;
    try {
      defaultValueTemp = getMarkdownFromJson(defaultValue);
    } catch {}
    const crepeInstance = new Crepe({
      root: containerRef.current,
      defaultValue: '',
      features: FEATURES,
      featureConfigs,
    });

    crepeInstance.editor
      .config((ctx) => {
        ctx.update(MentionsPluginOptions.key, (prev: MentionsOptions) => {
          return {
            ...prev,
            list: [],
            view: MentionsPluginDropdownView,
            onMention,
          };
        });
      })
      .use(mentions);

    crepeInstance
      .create()
      .then(() => {
        setEditorInstance(crepeInstance);
        const editorView = crepeInstance.editor.ctx.get(editorViewCtx);

        // 在这里直接设置 defaultValue
        if (defaultValue) {
          try {
            // markdwonType 不支持@粘贴
            if (defaultValueType === 'nodeType') {
              replaceAllForDraft(defaultValue, false)(crepeInstance.editor.ctx);
            } else {
              replaceAll(defaultValue, false)(crepeInstance.editor.ctx);
            }
          } catch (e) {
            console.error('Failed to replace defaultValue:', e);
          }
        }

        editorView.dom.addEventListener(
          'compositionstart',
          handleCompositionStart
        );
        editorView.dom.addEventListener('compositionend', handleCompositionEnd);
        // editorView.dom.addEventListener('selectionchange', () => {
        //   updateActiveMarks(crepeInstance.editor.ctx);
        // });
        const pluginsArr = [
          // mentionRemovePlugin(onMentionRemove),
          ...(!handlePaste ? [] : [pastePlugin(handlePaste)]),
          ...editorView.state.plugins,
          new Plugin({
            view: () => ({
              update: (view, prevState) => {
                const { state } = view;
                if (prevState && prevState.selection !== state.selection) {
                  updateActiveMarks(crepeInstance.editor.ctx);
                }
              },
            }),
          }),
        ];
        if (isForGroupAnnouncement) {
          pluginsArr.unshift(groupAnnouncementKeymap);
        } else {
          pluginsArr.unshift(customKeymap);
        }
        editorView.updateState(
          editorView.state.reconfigure({
            plugins: pluginsArr,
          })
        );

        // 初始化完成立刻focus
        editorView.focus();

        crepeInstance.on((listener) => {
          listener.markdownUpdated((ctx, markdown, preMarkDown) => {
            //
            onChange?.(markdown);
            updateActiveMarks(ctx);
          });
          if (onFocus) {
            listener.focus(onFocus);
          }
          if (onBlur) {
            listener.blur(onBlur);
          }
          listener.destroy((ctx) => {
            editorView.dom.removeEventListener(
              'compositionstart',
              handleCompositionStart
            );
            editorView.dom.removeEventListener(
              'compositionend',
              handleCompositionEnd
            );
          });
        });
      })
      .catch((error) => {
        console.error('Failed to initialize Milkdown editor:', error);
        onError?.(error);
      });

    if (disabledBtn) {
      crepeInstance.setReadonly(true);
    }

    return () => {
      if (editorInstance) {
        editorInstance.destroy();
        setEditorInstance(null);
      }
      crepeInstance?.destroy();
    };
  }, [disabledBtn, defaultValue, defaultValueType]);

  useEffect(() => {
    if (!editorInstance) {
      return;
    }

    editorInstance.editor.action((ctx) => {
      ctx.update(MentionsPluginOptions.key, (prev) => ({
        ...prev,
        list: groupMemberList,
      }));
    });
  }, [groupMemberList, editorInstance]);

  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      const linkEditElement = document.querySelector('.link-edit');
      if (!linkEditElement || !editorInstance?.editor) {
        return;
      }

      const { parentElement } = linkEditElement;
      const isClickInside =
        linkEditElement.contains(e.target as Node) ||
        (e.target as HTMLElement).classList.contains('milkdown'); // preview时点击编辑时，不应该清除

      const shouldRemove = parentElement?.dataset?.show === 'true';

      if (!isClickInside && shouldRemove) {
        removeLink();
      }
    };

    document.addEventListener('click', handleGlobalClick);
    return () => {
      document.removeEventListener('click', handleGlobalClick);
    };
  }, [editorInstance]);

  const replaceAllForDraft = (markdown: string, flush = false) => {
    return (ctx: Ctx): void => {
      try {
        const view = ctx.get(editorViewCtx);
        const schema = ctx.get(schemaCtx);
        const options = ctx.get(editorStateOptionsCtx);
        const plugins = ctx.get(prosePluginsCtx);
        const doc = Node.fromJSON(schema, JSON.parse(markdown));
        if (!doc) {
          return;
        }

        if (!flush) {
          const { state } = view;

          return view.dispatch(
            state.tr.replace(
              0,
              state.doc.content.size,
              new Slice(doc.content, 0, 0)
            )
          );
        }

        const state = EditorState.create({
          schema,
          doc,
          plugins,
          ...options,
        });

        view.updateState(state);
      } catch (e) {
        console.error('replaceAllForDraft', e);
      }
    };
  };

  const updateActiveMarks = (ctx) => {
    if (!ctx) {
      return;
    }

    const view = ctx.get(editorViewCtx);
    const { state } = view;
    const { selection, doc, schema } = state;
    const marks = new Set<string>();

    if (selection.empty) {
      // 获取光标位置的标记
      const storedMarks = selection.$from.marks();
      storedMarks.forEach((mark) => {
        if (mark.type === schema.marks.strong) {
          marks.add('bold');
        }
        if (mark.type === schema.marks.emphasis) {
          marks.add('italic');
        }
        if (mark.type === schema.marks.strike_through) {
          marks.add('strikethrough');
        }
        if (mark.type === schema.marks.link) {
          marks.add('link');
        }
        if (mark.type === schema.marks.code) {
          marks.add('code');
        }
      });
    } else {
      // 处理选中文本的情况 - 检查选中范围内是否有这些标记
      const { from, to } = selection;
      for (let pos = from; pos <= to; pos++) {
        const node = doc.nodeAt(pos);
        if (node) {
          node.marks.forEach((mark) => {
            if (mark.type === schema.marks.strong) {
              marks.add('bold');
            }
            if (mark.type === schema.marks.emphasis) {
              marks.add('italic');
            }
            if (mark.type === schema.marks.strike_through) {
              marks.add('strikethrough');
            }
            if (mark.type === schema.marks.link) {
              marks.add('link');
            }
            if (mark.type === schema.marks.code) {
              marks.add('code');
            }
          });
        }
      }
    }

    const { $from } = state.selection;
    for (let d = $from.depth; d >= 0; d--) {
      const node = $from.node(d);
      if (node.type === schema.nodes.ordered_list) {
        marks.add('order');
      }
      if (node.type === schema.nodes.bullet_list) {
        marks.add('disorder');
      }
      if (node.type === schema.nodes.blockquote) {
        marks.add('reply');
      }
      if (node.type === schema.nodes.code_block) {
        marks.add('codeBlock');
      }
    }

    setActiveMarks(marks);
  };
  const handlePaste = (view, event) => {
    const items = Array.from(event.clipboardData?.items || []);
    const hasImage = items.some((item) => item.type.startsWith('image'));
    if (hasImage) {
      event.preventDefault(); // 阻止粘贴图片
      return true;
    }

    onPaste?.(view, event);
    return false;
  };

  const handleCompositionStart = () => {
    onCompositionStart?.();
  };
  const handleCompositionEnd = () => {
    onCompositionEnd?.();
  };

  useImperativeHandle(
    ref,
    () => ({
      clearValue,
      focus: jsToMdFocus,
      getValue,
      getMentionList,
      getNodeJson,
      activeMarks,
    }),
    [editorInstance, activeMarks]
  );

  const getNodeJson = () => {
    const { state } = editorInstance?.editor?.ctx?.get(editorViewCtx) || {};
    const { doc } = state; // ProseMirror Node

    if (doc != null) {
      const json = doc.toJSON();
      return JSON.stringify(json);
    }
    return null;
  };
  const removeLink = () => {
    if (!editorInstance?.editor) {
      return;
    }

    const { editor } = editorInstance;
    const view = editor.ctx.get(editorViewCtx);
    const { selection } = view.state;

    editor.ctx.get(linkTooltipAPI.key).removeLink(selection.from, selection.to);
  };
  // 插入表情
  const insertEmoji = (emoji: string) => {
    if (!editorInstance?.editor) {
      return;
    }
    const { editor } = editorInstance;
    const view = editor.ctx.get(editorViewCtx);
    insertTextAtCursor(view, emoji);
  };

  // 清楚当前编辑内容
  const clearValue = () => {
    if (!editorInstance?.editor) {
      return;
    }
    try {
      const { editor } = editorInstance;
      const view = editor.ctx.get(editorViewCtx);
      const { tr } = view.state;
      tr.replaceWith(
        0,
        view.state.doc.content.size,
        view.state.schema.topNodeType.createAndFill() || []
      );
      view.dispatch(tr);
    } catch (error) {
      console.error('clearValue', error);
    }
  };

  const jsToMdFocus = () => {
    if (!editorInstance?.editor) {
      return;
    }
    try {
      const { editor } = editorInstance;
      const view = editor.ctx.get(editorViewCtx);
      view.focus();
    } catch (error) {
      console.error('editMdFocus', error);
    }
  };

  const editMdFocus = (e: React.MouseEvent<HTMLElement>): void => {
    // 获取目标元素
    const target = e?.target as HTMLElement;
    if (!target || !editorInstance?.editor) {
      return;
    }

    // 检查目标元素及其所有父元素是否包含特定类名
    let currentElement: HTMLElement | null = target;

    while (currentElement) {
      if (
        currentElement.classList?.contains('input-area') ||
        currentElement.tagName.toLowerCase() === 'milkdown-code-block-custom'
      ) {
        return;
      }
      currentElement = currentElement.parentElement;
    }

    try {
      const { editor } = editorInstance;
      const view = editor.ctx.get(editorViewCtx);
      view.focus();
    } catch (error) {
      console.error('editMdFocus:', error);
    }
  };

  const getValue = () => {
    const value = editorInstance?.getMarkdown();
    return value || '';
  };
  const getMentionList = () => {
    const mentions: [string] | [] = [];
    const { state } = editorInstance.editor.ctx.get(editorViewCtx) || {};
    if (state) {
      state.doc.descendants((node: any) => {
        if (node.type.name === 'mention') {
          mentions.push(node.attrs.id);
        }
      });
    }
    return mentions || '';
  };

  return (
    <div className={styles.editorContainer} onClick={editMdFocus}>
      {!isMultiSession && (
        <div className={styles.inputFooterWrapper}>
          <InputFooterRender
            customRequest={uploadMsg}
            showFormater={showFormater}
            changShowFormater={setShowFormater}
            pasteContainerRef={containerRef}
            insertEmoji={(val: string) => {
              insertEmoji(val);
            }}
            conversationID={conversationID}
            cloudUpload={cloudUpload}
            isGroup={isGroup}
          />
        </div>
      )}
      <div
        ref={containerRef}
        className={styles.inputWrap}
        style={{
          ...inputStyle,
          maxHeight: !isMultiSession && !hideBtn ? 'calc(100% - 40px)' : '100%',
        }}
      ></div>
    </div>
  );
};

const EditMd = forwardRef(EditMdComponent);
export default EditMd;
