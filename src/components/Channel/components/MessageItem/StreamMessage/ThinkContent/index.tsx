import React, { <PERSON> } from 'react';
import styles from './index.less';

interface ThinkContentProps {
  content: {
    think?: {
      answer: string;
    };
    plugins?: {
      name: string;
      status: 0 | 1;
    }[];
  };
}

const ThinkContent: FC<ThinkContentProps> = ({ content }) => {
  console.log('渲染ThinkContent', content);
  const thinkText = content?.think?.answer;
  const plugins = content?.plugins;

  if (!thinkText && !plugins?.length) {
    return;
  }

  return (
    <div className={styles.thinkContainer}>
      <div className={styles.thinkTitle}>思考和规划</div>
      <div className={styles.thinkContentContainer}>{thinkText}</div>
    </div>
  );
};

export default ThinkContent;
